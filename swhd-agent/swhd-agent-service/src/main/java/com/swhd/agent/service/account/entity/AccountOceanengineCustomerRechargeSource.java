package com.swhd.agent.service.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import com.swj.magiccube.mp.generic.typehandler.LongListTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 自助充值源表实体类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "tagent_account_oceanengine_customer_recharge_source" ,autoResultMap = true)
public class AccountOceanengineCustomerRechargeSource extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "充值源标题")
    private String title;

    @Schema(description = "客户ID列表")
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> customerIds;

    @Schema(description = "状态 0-关闭 1-开启")
    private Integer state;

}
